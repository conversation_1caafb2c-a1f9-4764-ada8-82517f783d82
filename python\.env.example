# Telegram Bot Configuration
BOT_TOKEN=your_bot_token_here
BOT_USERNAME=your_bot_username

# Admin Configuration (comma-separated list of admin user IDs)
ADMIN_IDS=123456789,987654321,555666777

# Channel Configuration (NO @ symbol for main channel, WITH @ for private logs)
MAIN_CHANNEL=YourMainChannel
PRIVATE_LOGS_CHANNEL=@YourPrivateChannel

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name?retryWrites=true&w=majority

# Bot Settings
MAINTENANCE_MODE=false
PER_REFER_AMOUNT=50
JOINING_BONUS_AMOUNT=50

# User Display Settings (what users see vs actual configured ranges)
USER_DISPLAY_REFERRAL_MAX=100
USER_DISPLAY_BONUS_MAX=100

# Withdrawal Settings
MIN_WITHDRAWAL_AMOUNT=100

# OTP Configuration
OTP_API_KEY=your_otp_api_key_here
OTP_API_URL=https://sms.renflair.in/V1.php

# Gift Configuration
GIFT_CHANNEL=YourGiftChannel
GIFT_AMOUNT=25

# Withdrawal Configuration
MIN_WITHDRAWAL_AMOUNT=100
WITHDRAWAL_AMOUNTS=100,200,400,600,800,1000

# Security Configuration
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=30

# Timezone
TIMEZONE=Asia/Kolkata

# Logging
LOG_LEVEL=INFO
LOG_FILE=bot.log
