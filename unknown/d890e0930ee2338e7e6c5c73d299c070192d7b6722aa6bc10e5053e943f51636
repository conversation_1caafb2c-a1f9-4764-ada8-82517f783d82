#!/usr/bin/env python3
"""
Test script to verify photo broadcast fix
"""

import asyncio
import logging
from services.admin_service import AdminService
from config.database import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_photo_broadcast_fix():
    """Test photo broadcast functionality after fix"""
    try:
        # Connect to database
        logger.info("Connecting to MongoDB...")
        if not await db_manager.connect():
            logger.error("Failed to connect to database")
            return

        # Initialize admin service
        admin_service = AdminService()

        # Test admin ID (replace with actual admin ID)
        test_admin_id = 8153676253

        logger.info("Testing photo broadcast fix...")

        # Test 1: Photo broadcast with caption
        logger.info("1. Testing photo broadcast with caption...")
        photo_with_caption_data = {
            "type": "photo",
            "photo": "AgACAgIAAxkBAAIBYmXh1234567890abcdef",  # Sample file_id
            "caption": "🧪 <b>Test Photo Broadcast</b>\n\nThis is a test photo with <i>HTML caption</i>."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=photo_with_caption_data,
            target_users=[test_admin_id]  # Only send to admin for testing
        )
        
        if result["success"]:
            logger.info(f"✅ Photo with caption broadcast created: {result['broadcast_id'][-8:]}")
            # Cancel immediately to avoid spam
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Photo with caption broadcast failed: {result.get('error')}")

        # Test 2: Photo broadcast without caption
        logger.info("2. Testing photo broadcast without caption...")
        photo_without_caption_data = {
            "type": "photo",
            "photo": "AgACAgIAAxkBAAIBYmXh1234567890abcdef",  # Sample file_id
            "caption": ""
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=photo_without_caption_data,
            target_users=[test_admin_id]
        )
        
        if result["success"]:
            logger.info(f"✅ Photo without caption broadcast created: {result['broadcast_id'][-8:]}")
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Photo without caption broadcast failed: {result.get('error')}")

        # Test 3: Verify photo message processing
        logger.info("3. Testing photo message processing...")
        
        from telegram import Bot
        from config.settings import settings
        
        bot = Bot(settings.BOT_TOKEN)
        
        # Test photo message processing (will fail with sample file_id, but tests the logic)
        try:
            photo_success = await admin_service._send_broadcast_message(bot, test_admin_id, photo_with_caption_data)
            logger.info(f"Photo message processing: {'✅ Success' if photo_success else '❌ Failed'}")
        except Exception as e:
            logger.info(f"Photo message processing: ❌ Failed (expected with sample file_id): {e}")

        # Test 4: Test session handler logic simulation
        logger.info("4. Testing session handler logic simulation...")
        
        # Simulate the message data creation logic from session handler
        class MockMessage:
            def __init__(self, has_photo=True, caption="Test caption"):
                self.text = None
                self.photo = [MockPhoto()] if has_photo else None
                self.video = None
                self.document = None
                self.audio = None
                self.caption = caption
        
        class MockPhoto:
            def __init__(self):
                self.file_id = "AgACAgIAAxkBAAIBYmXh1234567890abcdef"
        
        # Test photo message detection
        mock_photo_message = MockMessage(has_photo=True, caption="Hi")
        
        message_data = {}
        if mock_photo_message.text:
            message_data = {
                "type": "text",
                "text": mock_photo_message.text
            }
        elif mock_photo_message.photo:
            message_data = {
                "type": "photo",
                "photo": mock_photo_message.photo[-1].file_id,
                "caption": mock_photo_message.caption or ""
            }
        elif mock_photo_message.video:
            message_data = {
                "type": "video",
                "video": mock_photo_message.video.file_id,
                "caption": mock_photo_message.caption or ""
            }
        elif mock_photo_message.document:
            message_data = {
                "type": "document",
                "document": mock_photo_message.document.file_id,
                "caption": mock_photo_message.caption or ""
            }
        elif mock_photo_message.audio:
            message_data = {
                "type": "audio",
                "audio": mock_photo_message.audio.file_id,
                "caption": mock_photo_message.caption or ""
            }
        
        if message_data.get("type") == "photo":
            logger.info("✅ Photo message detection working correctly")
            logger.info(f"   Photo file_id: {message_data['photo']}")
            logger.info(f"   Caption: '{message_data['caption']}'")
        else:
            logger.error("❌ Photo message detection failed")

        logger.info("✅ Photo broadcast fix test completed!")

        # Disconnect from database
        await db_manager.disconnect()

    except Exception as e:
        logger.error(f"Error during photo broadcast fix test: {e}")

if __name__ == "__main__":
    asyncio.run(test_photo_broadcast_fix())
