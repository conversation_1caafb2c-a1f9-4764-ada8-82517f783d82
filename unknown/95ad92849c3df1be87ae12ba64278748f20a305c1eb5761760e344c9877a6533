#!/usr/bin/env python3
"""
Script to clean up stale broadcast sessions
"""

import asyncio
import logging
from services.admin_service import AdminService
from config.database import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    """Clean up stale broadcast sessions"""
    try:
        # Connect to database
        logger.info("Connecting to MongoDB...")
        if not await db_manager.connect():
            logger.error("Failed to connect to database")
            return

        # Initialize admin service
        admin_service = AdminService()

        # Get all active broadcasts
        active_broadcasts = await admin_service.get_all_active_broadcasts()
        logger.info(f"Found {len(active_broadcasts)} active broadcast sessions")

        if active_broadcasts:
            for broadcast in active_broadcasts:
                broadcast_id = broadcast['broadcast_id']
                admin_id = broadcast['admin_id']
                created_at = broadcast.get('created_at', 0)
                
                logger.info(f"Active broadcast: {broadcast_id[-8:]} by admin {admin_id} (created: {created_at})")

        # Clean up stale sessions (older than 1 hour for testing)
        cleaned_count = await admin_service.cleanup_stale_broadcast_sessions(max_age_hours=1)
        logger.info(f"Cleaned up {cleaned_count} stale broadcast sessions")

        # Disconnect from database
        await db_manager.disconnect()
        logger.info("Cleanup completed successfully")

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

if __name__ == "__main__":
    asyncio.run(main())
