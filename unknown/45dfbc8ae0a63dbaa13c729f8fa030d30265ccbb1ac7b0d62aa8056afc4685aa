"""
Data migration script from PHP JSON files to MongoDB
Maintains identical data structure for seamless migration
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List

# Add the python directory to the path
sys.path.append(str(Path(__file__).parent))

from config.database import db_manager, get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataMigrator:
    """Migrates data from PHP JSON files to MongoDB"""
    
    def __init__(self, php_data_dir: str):
        self.php_data_dir = Path(php_data_dir)
        self.migration_stats = {
            'users': 0,
            'admin_settings': 0,
            'tasks': 0,
            'task_submissions': 0,
            'gift_codes': 0,
            'custom_referrals': 0,
            'sessions': 0,
            'broadcast_logs': 0,
            'broadcast_sessions': 0,
            'bot_info': 0
        }
    
    async def migrate_all_data(self) -> bool:
        """Migrate all data from PHP JSON files to MongoDB"""
        try:
            logger.info("Starting data migration from PHP to MongoDB...")
            
            # Connect to database
            if not await db_manager.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Migrate each data type
            await self._migrate_users()
            await self._migrate_admin_settings()
            await self._migrate_tasks()
            await self._migrate_task_submissions()
            await self._migrate_gift_codes()
            await self._migrate_custom_referrals()
            await self._migrate_sessions()
            await self._migrate_broadcast_logs()
            await self._migrate_broadcast_sessions()
            await self._migrate_bot_info()
            
            # Print migration summary
            self._print_migration_summary()
            
            logger.info("Data migration completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during data migration: {e}")
            return False
        finally:
            await db_manager.disconnect()
    
    async def _migrate_users(self):
        """Migrate users.json to MongoDB"""
        try:
            users_file = self.php_data_dir / 'users.json'
            if not users_file.exists():
                logger.warning("users.json not found, skipping users migration")
                return
            
            logger.info("Migrating users data...")
            
            with open(users_file, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
            
            if not users_data:
                logger.info("No users data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['users'])
            
            # Convert PHP associative array to list of documents
            users_list = []
            for user_id_str, user_data in users_data.items():
                # Ensure user_id is integer
                user_data['user_id'] = int(user_id_str)
                
                # Add timestamps if missing
                if 'created_at' not in user_data:
                    user_data['created_at'] = get_current_timestamp()
                if 'updated_at' not in user_data:
                    user_data['updated_at'] = get_current_timestamp()
                
                # Ensure required fields exist with defaults
                self._ensure_user_fields(user_data)
                
                users_list.append(user_data)
            
            # Insert users in batches
            batch_size = 100
            for i in range(0, len(users_list), batch_size):
                batch = users_list[i:i + batch_size]
                await collection.insert_many(batch, ordered=False)
                self.migration_stats['users'] += len(batch)
                logger.info(f"Migrated {self.migration_stats['users']}/{len(users_list)} users")
            
            logger.info(f"Successfully migrated {self.migration_stats['users']} users")
            
        except Exception as e:
            logger.error(f"Error migrating users: {e}")
    
    async def _migrate_admin_settings(self):
        """Migrate admin_settings.json to MongoDB"""
        try:
            admin_file = self.php_data_dir / 'admin_settings.json'
            if not admin_file.exists():
                logger.warning("admin_settings.json not found, skipping admin settings migration")
                return
            
            logger.info("Migrating admin settings data...")
            
            with open(admin_file, 'r', encoding='utf-8') as f:
                admin_data = json.load(f)
            
            if not admin_data:
                logger.info("No admin settings data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            # Convert PHP associative array to list of documents
            admin_list = []
            for admin_id_str, admin_settings in admin_data.items():
                # Ensure admin_id is integer
                admin_settings['admin_id'] = int(admin_id_str)
                
                # Add timestamps if missing
                if 'created_at' not in admin_settings:
                    admin_settings['created_at'] = get_current_timestamp()
                if 'updated_at' not in admin_settings:
                    admin_settings['updated_at'] = get_current_timestamp()
                
                # Ensure required fields exist with defaults
                self._ensure_admin_fields(admin_settings)
                
                admin_list.append(admin_settings)
            
            # Insert admin settings
            if admin_list:
                await collection.insert_many(admin_list, ordered=False)
                self.migration_stats['admin_settings'] = len(admin_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['admin_settings']} admin settings")
            
        except Exception as e:
            logger.error(f"Error migrating admin settings: {e}")
    
    async def _migrate_tasks(self):
        """Migrate tasks.json to MongoDB"""
        try:
            tasks_file = self.php_data_dir / 'tasks.json'
            if not tasks_file.exists():
                logger.warning("tasks.json not found, skipping tasks migration")
                return
            
            logger.info("Migrating tasks data...")
            
            with open(tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)
            
            if not tasks_data:
                logger.info("No tasks data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['tasks'])
            
            # Ensure required fields for each task
            for task in tasks_data:
                if 'updated_at' not in task:
                    task['updated_at'] = get_current_timestamp()
                self._ensure_task_fields(task)
            
            # Insert tasks
            await collection.insert_many(tasks_data, ordered=False)
            self.migration_stats['tasks'] = len(tasks_data)
            
            logger.info(f"Successfully migrated {self.migration_stats['tasks']} tasks")
            
        except Exception as e:
            logger.error(f"Error migrating tasks: {e}")
    
    async def _migrate_task_submissions(self):
        """Migrate task_submissions.json to MongoDB"""
        try:
            submissions_file = self.php_data_dir / 'task_submissions.json'
            if not submissions_file.exists():
                logger.warning("task_submissions.json not found, skipping task submissions migration")
                return
            
            logger.info("Migrating task submissions data...")
            
            with open(submissions_file, 'r', encoding='utf-8') as f:
                submissions_data = json.load(f)
            
            if not submissions_data:
                logger.info("No task submissions data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['task_submissions'])
            
            # Convert PHP associative array to list if needed
            if isinstance(submissions_data, dict):
                submissions_list = list(submissions_data.values())
            else:
                submissions_list = submissions_data
            
            # Ensure required fields for each submission
            for submission in submissions_list:
                if 'updated_at' not in submission:
                    submission['updated_at'] = get_current_timestamp()
                self._ensure_submission_fields(submission)
            
            # Insert submissions
            if submissions_list:
                await collection.insert_many(submissions_list, ordered=False)
                self.migration_stats['task_submissions'] = len(submissions_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['task_submissions']} task submissions")
            
        except Exception as e:
            logger.error(f"Error migrating task submissions: {e}")
    
    async def _migrate_gift_codes(self):
        """Migrate gift_codes.json to MongoDB"""
        try:
            gift_codes_file = self.php_data_dir / 'gift_codes.json'
            if not gift_codes_file.exists():
                logger.warning("gift_codes.json not found, skipping gift codes migration")
                return
            
            logger.info("Migrating gift codes data...")
            
            with open(gift_codes_file, 'r', encoding='utf-8') as f:
                gift_codes_data = json.load(f)
            
            if not gift_codes_data:
                logger.info("No gift codes data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            # Convert PHP associative array to list if needed
            if isinstance(gift_codes_data, dict):
                gift_codes_list = list(gift_codes_data.values())
            else:
                gift_codes_list = gift_codes_data
            
            # Ensure required fields for each gift code
            for gift_code in gift_codes_list:
                if 'updated_at' not in gift_code:
                    gift_code['updated_at'] = get_current_timestamp()
                self._ensure_gift_code_fields(gift_code)
            
            # Insert gift codes
            if gift_codes_list:
                await collection.insert_many(gift_codes_list, ordered=False)
                self.migration_stats['gift_codes'] = len(gift_codes_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['gift_codes']} gift codes")
            
        except Exception as e:
            logger.error(f"Error migrating gift codes: {e}")
    
    async def _migrate_custom_referrals(self):
        """Migrate custom_referral_links.json to MongoDB"""
        try:
            custom_ref_file = self.php_data_dir / 'custom_referral_links.json'
            if not custom_ref_file.exists():
                logger.warning("custom_referral_links.json not found, skipping custom referrals migration")
                return
            
            logger.info("Migrating custom referrals data...")
            
            with open(custom_ref_file, 'r', encoding='utf-8') as f:
                custom_ref_data = json.load(f)
            
            if not custom_ref_data:
                logger.info("No custom referrals data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            # Convert PHP associative array to list if needed
            if isinstance(custom_ref_data, dict):
                custom_ref_list = list(custom_ref_data.values())
            else:
                custom_ref_list = custom_ref_data
            
            # Ensure required fields for each custom referral
            for custom_ref in custom_ref_list:
                if 'updated_at' not in custom_ref:
                    custom_ref['updated_at'] = get_current_timestamp()
                if 'usage_count' not in custom_ref:
                    custom_ref['usage_count'] = 0
            
            # Insert custom referrals
            if custom_ref_list:
                await collection.insert_many(custom_ref_list, ordered=False)
                self.migration_stats['custom_referrals'] = len(custom_ref_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['custom_referrals']} custom referrals")
            
        except Exception as e:
            logger.error(f"Error migrating custom referrals: {e}")
    
    async def _migrate_sessions(self):
        """Migrate user_sessions.json to MongoDB"""
        try:
            sessions_file = self.php_data_dir / 'user_sessions.json'
            if not sessions_file.exists():
                logger.warning("user_sessions.json not found, skipping sessions migration")
                return
            
            logger.info("Migrating sessions data...")
            
            with open(sessions_file, 'r', encoding='utf-8') as f:
                sessions_data = json.load(f)
            
            if not sessions_data:
                logger.info("No sessions data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['sessions'])
            
            # Convert PHP associative array to list of documents
            sessions_list = []
            for user_id_str, session_data in sessions_data.items():
                session_data['user_id'] = int(user_id_str)
                
                # Add timestamps if missing
                if 'created_at' not in session_data:
                    session_data['created_at'] = get_current_timestamp()
                if 'updated_at' not in session_data:
                    session_data['updated_at'] = get_current_timestamp()
                
                sessions_list.append(session_data)
            
            # Insert sessions
            if sessions_list:
                await collection.insert_many(sessions_list, ordered=False)
                self.migration_stats['sessions'] = len(sessions_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['sessions']} sessions")
            
        except Exception as e:
            logger.error(f"Error migrating sessions: {e}")
    
    async def _migrate_broadcast_logs(self):
        """Migrate broadcast_logs.json to MongoDB"""
        try:
            broadcast_logs_file = self.php_data_dir / 'broadcast_logs.json'
            if not broadcast_logs_file.exists():
                logger.warning("broadcast_logs.json not found, skipping broadcast logs migration")
                return
            
            logger.info("Migrating broadcast logs data...")
            
            with open(broadcast_logs_file, 'r', encoding='utf-8') as f:
                broadcast_logs_data = json.load(f)
            
            if not broadcast_logs_data:
                logger.info("No broadcast logs data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            
            # Convert to list if needed
            if isinstance(broadcast_logs_data, dict):
                broadcast_logs_list = list(broadcast_logs_data.values())
            else:
                broadcast_logs_list = broadcast_logs_data
            
            # Insert broadcast logs
            if broadcast_logs_list:
                await collection.insert_many(broadcast_logs_list, ordered=False)
                self.migration_stats['broadcast_logs'] = len(broadcast_logs_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['broadcast_logs']} broadcast logs")
            
        except Exception as e:
            logger.error(f"Error migrating broadcast logs: {e}")
    
    async def _migrate_broadcast_sessions(self):
        """Migrate broadcast_sessions.json to MongoDB"""
        try:
            broadcast_sessions_file = self.php_data_dir / 'broadcast_sessions.json'
            if not broadcast_sessions_file.exists():
                logger.warning("broadcast_sessions.json not found, skipping broadcast sessions migration")
                return
            
            logger.info("Migrating broadcast sessions data...")
            
            with open(broadcast_sessions_file, 'r', encoding='utf-8') as f:
                broadcast_sessions_data = json.load(f)
            
            if not broadcast_sessions_data:
                logger.info("No broadcast sessions data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])
            
            # Convert to list if needed
            if isinstance(broadcast_sessions_data, dict):
                broadcast_sessions_list = list(broadcast_sessions_data.values())
            else:
                broadcast_sessions_list = broadcast_sessions_data
            
            # Insert broadcast sessions
            if broadcast_sessions_list:
                await collection.insert_many(broadcast_sessions_list, ordered=False)
                self.migration_stats['broadcast_sessions'] = len(broadcast_sessions_list)
            
            logger.info(f"Successfully migrated {self.migration_stats['broadcast_sessions']} broadcast sessions")
            
        except Exception as e:
            logger.error(f"Error migrating broadcast sessions: {e}")
    
    async def _migrate_bot_info(self):
        """Migrate bot_info.json to MongoDB"""
        try:
            bot_info_file = self.php_data_dir / 'bot_info.json'
            if not bot_info_file.exists():
                logger.warning("bot_info.json not found, skipping bot info migration")
                return
            
            logger.info("Migrating bot info data...")
            
            with open(bot_info_file, 'r', encoding='utf-8') as f:
                bot_info_data = json.load(f)
            
            if not bot_info_data:
                logger.info("No bot info data to migrate")
                return
            
            collection = await get_collection(COLLECTIONS['bot_info'])
            
            # Add timestamp if missing
            if 'updated_at' not in bot_info_data:
                bot_info_data['updated_at'] = get_current_timestamp()
            
            # Insert bot info
            await collection.insert_one(bot_info_data)
            self.migration_stats['bot_info'] = 1
            
            logger.info(f"Successfully migrated bot info")
            
        except Exception as e:
            logger.error(f"Error migrating bot info: {e}")
    
    def _ensure_user_fields(self, user_data: Dict[str, Any]):
        """Ensure user has all required fields with defaults"""
        defaults = {
            'banned': False,
            'referred': False,
            'referred_by': 'None',
            'joining_bonus_got': 0,
            'balance': 0,
            'successful_withdraw': 0,
            'withdraw_under_review': 0,
            'gift_claimed': False,
            'claimed_levels': [],
            'account_info': {},
            'promotion_report': [],
            'withdrawal_reports': []
        }
        
        for field, default_value in defaults.items():
            if field not in user_data:
                user_data[field] = default_value
        
        # Ensure account_info has required fields
        account_defaults = {
            'name': '',
            'ifsc': '',
            'email': '',
            'account_number': '',
            'mobile_number': '',
            'withdrawal_method': '',
            'usdt_address': '',
            'binance_id': ''
        }
        
        for field, default_value in account_defaults.items():
            if field not in user_data['account_info']:
                user_data['account_info'][field] = default_value
    
    def _ensure_admin_fields(self, admin_data: Dict[str, Any]):
        """Ensure admin settings have all required fields with defaults"""
        defaults = {
            'main_channel': '',
            'private_logs_channel': '',
            'maintenance_status': 'Off',
            'otp_website_api_key': '',
            'per_refer_amount': 0,
            'joining_bonus_amount': 0,
            'gift_channel': '',
            'gift_amount': 0,
            'force_sub_channels': [],
            'level_rewards_enabled': True,
            'level_rewards_config': {
                'referral_requirements': [1, 5, 10, 15, 20, 25],
                'bonus_amounts': [2, 10, 15, 20, 25, 30]
            },
            'withdrawal_settings': {
                'enabled': True,
                'tax_type': 'none',
                'tax_amount': 0,
                'tax_percentage': 0
            }
        }
        
        for field, default_value in defaults.items():
            if field not in admin_data:
                admin_data[field] = default_value
    
    def _ensure_task_fields(self, task_data: Dict[str, Any]):
        """Ensure task has all required fields with defaults"""
        defaults = {
            'status': 'active',
            'created_by': 0,
            'media_url': '',
            'reward_amount': 0
        }
        
        for field, default_value in defaults.items():
            if field not in task_data:
                task_data[field] = default_value
    
    def _ensure_submission_fields(self, submission_data: Dict[str, Any]):
        """Ensure submission has all required fields with defaults"""
        defaults = {
            'status': 'pending',
            'screenshot_url': '',
            'admin_notes': ''
        }
        
        for field, default_value in defaults.items():
            if field not in submission_data:
                submission_data[field] = default_value
    
    def _ensure_gift_code_fields(self, gift_code_data: Dict[str, Any]):
        """Ensure gift code has all required fields with defaults"""
        defaults = {
            'status': 'active',
            'usage_count': 0,
            'usage_limit': 1,
            'amount': 0
        }
        
        for field, default_value in defaults.items():
            if field not in gift_code_data:
                gift_code_data[field] = default_value
    
    def _print_migration_summary(self):
        """Print migration summary"""
        logger.info("\n" + "="*50)
        logger.info("MIGRATION SUMMARY")
        logger.info("="*50)
        
        total_records = 0
        for collection_name, count in self.migration_stats.items():
            logger.info(f"{collection_name.ljust(20)}: {count:,} records")
            total_records += count
        
        logger.info("-"*50)
        logger.info(f"{'TOTAL'.ljust(20)}: {total_records:,} records")
        logger.info("="*50)

async def main():
    """Main migration function"""
    if len(sys.argv) != 2:
        print("Usage: python migrate_data.py <path_to_php_data_directory>")
        print("Example: python migrate_data.py ../data")
        sys.exit(1)
    
    php_data_dir = sys.argv[1]
    
    if not os.path.exists(php_data_dir):
        print(f"Error: Directory '{php_data_dir}' does not exist")
        sys.exit(1)
    
    migrator = DataMigrator(php_data_dir)
    success = await migrator.migrate_all_data()
    
    if success:
        print("\n✅ Data migration completed successfully!")
        print("Your PHP JSON data has been migrated to MongoDB.")
        print("You can now start the Python bot with: python main.py")
    else:
        print("\n❌ Data migration failed!")
        print("Please check the logs for error details.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
