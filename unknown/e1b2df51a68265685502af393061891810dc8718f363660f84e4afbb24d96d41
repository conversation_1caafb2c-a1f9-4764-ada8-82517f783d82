#!/usr/bin/env python3
"""
Test script to verify multimedia broadcast functionality
"""

import asyncio
import logging
from services.admin_service import AdminService
from config.database import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_multimedia_broadcast():
    """Test multimedia broadcast functionality"""
    try:
        # Connect to database
        logger.info("Connecting to MongoDB...")
        if not await db_manager.connect():
            logger.error("Failed to connect to database")
            return

        # Initialize admin service
        admin_service = AdminService()

        # Test admin ID (replace with actual admin ID)
        test_admin_id = 8153676253

        logger.info("Testing multimedia broadcast functionality...")

        # Test 1: Text broadcast
        logger.info("1. Testing text broadcast...")
        text_message_data = {
            "type": "text",
            "text": "🧪 <b>Test Text Broadcast</b>\n\nThis is a test text message with <i>HTML formatting</i>."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=text_message_data,
            target_users=[test_admin_id]  # Only send to admin for testing
        )
        
        if result["success"]:
            logger.info(f"✅ Text broadcast created: {result['broadcast_id'][-8:]}")
            # Cancel immediately to avoid spam
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Text broadcast failed: {result.get('error')}")

        # Test 2: Photo broadcast
        logger.info("2. Testing photo broadcast...")
        photo_message_data = {
            "type": "photo",
            "photo": "https://via.placeholder.com/300x200.png?text=Test+Photo",
            "caption": "🧪 <b>Test Photo Broadcast</b>\n\nThis is a test photo with caption."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=photo_message_data,
            target_users=[test_admin_id]
        )
        
        if result["success"]:
            logger.info(f"✅ Photo broadcast created: {result['broadcast_id'][-8:]}")
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Photo broadcast failed: {result.get('error')}")

        # Test 3: Video broadcast
        logger.info("3. Testing video broadcast...")
        video_message_data = {
            "type": "video",
            "video": "BAACAgIAAxkBAAIBYmXh1234567890abcdef",  # Sample file_id
            "caption": "🧪 <b>Test Video Broadcast</b>\n\nThis is a test video with caption."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=video_message_data,
            target_users=[test_admin_id]
        )
        
        if result["success"]:
            logger.info(f"✅ Video broadcast created: {result['broadcast_id'][-8:]}")
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Video broadcast failed: {result.get('error')}")

        # Test 4: Document broadcast
        logger.info("4. Testing document broadcast...")
        document_message_data = {
            "type": "document",
            "document": "BAADBAADYwADBREAAUmaeAI",  # Sample file_id
            "caption": "🧪 <b>Test Document Broadcast</b>\n\nThis is a test document with caption."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=document_message_data,
            target_users=[test_admin_id]
        )
        
        if result["success"]:
            logger.info(f"✅ Document broadcast created: {result['broadcast_id'][-8:]}")
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Document broadcast failed: {result.get('error')}")

        # Test 5: Audio broadcast
        logger.info("5. Testing audio broadcast...")
        audio_message_data = {
            "type": "audio",
            "audio": "CQACAgIAAxkBAAIBYmXh1234567890abcdef",  # Sample file_id
            "caption": "🧪 <b>Test Audio Broadcast</b>\n\nThis is a test audio with caption."
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=audio_message_data,
            target_users=[test_admin_id]
        )
        
        if result["success"]:
            logger.info(f"✅ Audio broadcast created: {result['broadcast_id'][-8:]}")
            await admin_service.cancel_broadcast(result["broadcast_id"], test_admin_id)
            await admin_service.cleanup_broadcast_session(result["broadcast_id"])
        else:
            logger.error(f"❌ Audio broadcast failed: {result.get('error')}")

        # Test 6: Check broadcast message processing
        logger.info("6. Testing broadcast message processing...")
        
        # Test the _send_broadcast_message method directly
        from telegram import Bot
        from config.settings import settings
        
        bot = Bot(settings.BOT_TOKEN)
        
        # Test text message processing
        text_success = await admin_service._send_broadcast_message(bot, test_admin_id, text_message_data)
        logger.info(f"Text message processing: {'✅ Success' if text_success else '❌ Failed'}")
        
        # Test photo message processing (will fail with placeholder URL, but tests the logic)
        try:
            photo_success = await admin_service._send_broadcast_message(bot, test_admin_id, photo_message_data)
            logger.info(f"Photo message processing: {'✅ Success' if photo_success else '❌ Failed'}")
        except Exception as e:
            logger.info(f"Photo message processing: ❌ Failed (expected with placeholder URL): {e}")

        logger.info("✅ Multimedia broadcast functionality test completed!")

        # Disconnect from database
        await db_manager.disconnect()

    except Exception as e:
        logger.error(f"Error during multimedia broadcast test: {e}")

if __name__ == "__main__":
    asyncio.run(test_multimedia_broadcast())
