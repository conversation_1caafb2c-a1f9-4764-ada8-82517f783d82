"""
Promotion Report Service
Handles promotion report data retrieval and pagination
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from config.database import get_collection, COLLECTIONS
from services.user_service import UserService

logger = logging.getLogger(__name__)

class PromotionReportService:
    """Service for promotion report operations with pagination"""
    
    def __init__(self):
        self.user_service = UserService()
    
    async def get_user_referrals_paginated(
        self, 
        user_id: int, 
        page: int = 1, 
        per_page: int = 10
    ) -> Dict[str, Any]:
        """
        Get paginated list of users referred by this user
        
        Args:
            user_id: ID of the user whose referrals to fetch
            page: Page number (1-based)
            per_page: Number of items per page
            
        Returns:
            Dict containing referrals data, pagination info, and metadata
        """
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Calculate skip value for pagination
            skip = (page - 1) * per_page

            # Get total count of referrals (referred_by is stored as string)
            total_referrals = await collection.count_documents({"referred_by": str(user_id)})

            # Calculate total pages
            total_pages = (total_referrals + per_page - 1) // per_page if total_referrals > 0 else 1

            # Ensure page is within valid range
            page = max(1, min(page, total_pages))

            # Get paginated referrals sorted by creation date (newest first)
            cursor = collection.find(
                {"referred_by": str(user_id)},
                {"user_id": 1, "first_name": 1, "username": 1, "created_at": 1, "_id": 0}
            ).sort("created_at", -1).skip(skip).limit(per_page)
            
            referrals = await cursor.to_list(length=per_page)

            # Format referrals for display
            formatted_referrals = []
            for i, referral in enumerate(referrals):
                # Calculate the actual number for this referral (considering pagination)
                referral_number = skip + i + 1
                
                # Format username display
                username = referral.get('username', '')
                if username:
                    username_display = f"@{username}"
                else:
                    username_display = "@User"
                
                formatted_referrals.append({
                    'number': referral_number,
                    'user_id': referral.get('user_id'),
                    'first_name': referral.get('first_name', 'Unknown'),
                    'username': username,
                    'username_display': username_display,
                    'created_at': referral.get('created_at')
                })
            
            return {
                'success': True,
                'referrals': formatted_referrals,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'per_page': per_page,
                    'total_referrals': total_referrals,
                    'has_next': page < total_pages,
                    'has_previous': page > 1
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting paginated referrals for user {user_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'referrals': [],
                'pagination': {
                    'current_page': 1,
                    'total_pages': 1,
                    'per_page': per_page,
                    'total_referrals': 0,
                    'has_next': False,
                    'has_previous': False
                }
            }
    
    async def get_referral_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get referral summary statistics for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dict containing referral statistics
        """
        try:
            # Get user data to access promotion_report
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'total_referrals': 0,
                    'total_earnings': 0
                }
            
            promotion_report = user.get('promotion_report', [])
            total_earnings = sum(report.get('amount_got', 0) for report in promotion_report)
            
            # Also get actual referral count from database (referred_by is stored as string)
            collection = await get_collection(COLLECTIONS['users'])
            actual_referral_count = await collection.count_documents({"referred_by": str(user_id)})
            
            return {
                'total_referrals': actual_referral_count,
                'total_earnings': total_earnings,
                'promotion_report_count': len(promotion_report)
            }
            
        except Exception as e:
            logger.error(f"Error getting referral summary for user {user_id}: {e}")
            return {
                'total_referrals': 0,
                'total_earnings': 0,
                'promotion_report_count': 0
            }
