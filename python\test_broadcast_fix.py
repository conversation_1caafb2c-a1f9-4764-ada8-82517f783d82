#!/usr/bin/env python3
"""
Test script to verify broadcast session management fix
"""

import asyncio
import logging
from services.admin_service import AdminService
from config.database import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_broadcast_management():
    """Test broadcast session management"""
    try:
        # Connect to database
        logger.info("Connecting to MongoDB...")
        if not await db_manager.connect():
            logger.error("Failed to connect to database")
            return

        # Initialize admin service
        admin_service = AdminService()

        # Test admin ID (replace with actual admin ID)
        test_admin_id = 8153676253

        # Check for active broadcasts
        logger.info("Checking for active broadcasts...")
        active_broadcast = await admin_service.get_active_broadcast(test_admin_id)
        
        if active_broadcast:
            logger.info(f"Found active broadcast: {active_broadcast['broadcast_id'][-8:]}")
            logger.info("This should not happen after cleanup!")
        else:
            logger.info("✅ No active broadcasts found - cleanup was successful!")

        # Test creating a broadcast session
        logger.info("Testing broadcast session creation...")
        
        message_data = {
            "type": "text",
            "text": "Test broadcast message"
        }
        
        result = await admin_service.start_message_broadcast(
            admin_id=test_admin_id,
            message_data=message_data,
            target_users=[test_admin_id]  # Only send to admin for testing
        )
        
        if result["success"]:
            logger.info(f"✅ Broadcast created successfully: {result['broadcast_id'][-8:]}")
            
            # Test cancelling the broadcast
            broadcast_id = result["broadcast_id"]
            logger.info("Testing broadcast cancellation...")
            
            cancel_success = await admin_service.cancel_broadcast(broadcast_id, test_admin_id)
            
            if cancel_success:
                logger.info("✅ Broadcast cancelled successfully!")
                
                # Verify it's marked as cancelled
                session = await admin_service.get_broadcast_session(broadcast_id)
                if session and session.get("cancelled", False):
                    logger.info("✅ Broadcast session correctly marked as cancelled")
                else:
                    logger.error("❌ Broadcast session not marked as cancelled")
                
                # Clean up the test session
                await admin_service.cleanup_broadcast_session(broadcast_id)
                logger.info("✅ Test broadcast session cleaned up")
                
            else:
                logger.error("❌ Failed to cancel broadcast")
        else:
            logger.error(f"❌ Failed to create broadcast: {result.get('error', 'Unknown error')}")

        # Final check - should be no active broadcasts
        final_check = await admin_service.get_active_broadcast(test_admin_id)
        if final_check:
            logger.error(f"❌ Still found active broadcast: {final_check['broadcast_id'][-8:]}")
        else:
            logger.info("✅ No active broadcasts after test - all good!")

        # Disconnect from database
        await db_manager.disconnect()
        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Error during test: {e}")

if __name__ == "__main__":
    asyncio.run(test_broadcast_management())
